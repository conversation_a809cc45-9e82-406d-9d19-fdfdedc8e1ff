# Models package initialization
# Import all models to ensure they are registered with SQLAlchemy

# Import base models first
from .Category.CategoryModel import Category
from .Ingredients.IngredientsModel import Ingredient

# Import product models
from .Doners.DonerModel import Doner
from .Kebabs.KebabModel import <PERSON><PERSON>b
from .Salads.SaladModel import Salad
from .Drinks.DrinksModel import Drink

# Import menu models first as other models depend on it
from .Menu.MenuModel import Menu
from .Menu.MenuItemModel import MenuItem

# Import user-related models
from .User.UserModel import User, UserRole
from .User.AddressModel import Address
from .User.ReviewModel import Review
from .User.CommentModel import Comment

# Import order and cart models
from .Order.OrderModel import Order
from .Order.OrderItem import OrderItem
from .Cart.CartModel import Cart

# Import message model
from .Message.MessageModel import Message

# Export all models
__all__ = [
    'Category',
    'Ingredient',
    'Doner',
    'Kebab',
    'Salad',
    'Drink',
    'User',
    'UserRole',
    'Address',
    'Review',
    'Comment',
    'Order',
    'OrderItem',
    'Cart',
    'Menu',
    'MenuItem',
    'Message'
]
