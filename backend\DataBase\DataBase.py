from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from dotenv import load_dotenv
import os

load_dotenv()

SQLALCHEMY_DATABASE_URL = os.getenv("SQLALCHEMY_DATABASE_URL")

# Create async engine
engine = create_async_engine(SQLALCHEMY_DATABASE_URL, echo=True)

# Create async session
AsyncSessionLocal = sessionmaker(
    bind=engine, class_=AsyncSession, expire_on_commit=False
)

# Base class for models
Base = declarative_base()

# Dependency for getting DB session in routes
async def get_db():
    async with AsyncSessionLocal() as session:
        yield session

# Add the missing functions that are imported in main.py
async def init_database_pool():
    """Initialize database connection pool"""
    # Connection pool is automatically handled by SQLAlchemy engine
    # This function can be used for any additional initialization if needed
    pass

async def close_database_pool():
    """Close database connection pool"""
    await engine.dispose()
