
from sqlalchemy.orm import Session
from ...Models.Drinks.DrinksModel import Drink
from ...Models.Category.CategoryModel import Category

def seed_drinks(db: Session):
    # Check if drinks already exist
    existing_drinks = db.query(Drink).count()
    if existing_drinks > 0:
        print(f"Drinks already exist ({existing_drinks} found), skipping drink seeding.")
        return

    drinks_to_seed = [
        {
            "name": "Coca-Cola",
            "description": "Classic Coca-Cola in a can.",
            "price": 2.50,
            "size_ml": 330,
            "is_carbonated": True,
            "image_url": "/images/drinks/coca_cola.jpg",
            "is_available": True,
            "category_name": "Drink"
        },
        {
            "name": "Ayran",
            "description": "A refreshing Turkish yogurt drink.",
            "price": 2.00,
            "size_ml": 250,
            "is_carbonated": False,
            "image_url": "/images/drinks/ayran.jpg",
            "is_available": True,
            "category_name": "Drink"
        },
        {
            "name": "<PERSON><PERSON>",
            "description": "Orange flavored carbonated drink.",
            "price": 2.50,
            "size_ml": 330,
            "is_carbonated": True,
            "image_url": "/images/drinks/fanta.jpg",
            "is_available": True,
            "category_name": "Drink"
        },
        {
            "name": "Sprite",
            "description": "Lemon-lime flavored carbonated drink.",
            "price": 2.50,
            "size_ml": 330,
            "is_carbonated": True,
            "image_url": "/images/drinks/sprite.jpg",
            "is_available": True,
            "category_name": "Drink"
        },
        {
            "name": "Still Water",
            "description": "A bottle of still mineral water.",
            "price": 1.50,
            "size_ml": 500,
            "is_carbonated": False,
            "image_url": "/images/drinks/water.jpg",
            "is_available": True,
            "category_name": "Drink"
        },
        {
            "name": "Sparkling Water",
            "description": "A bottle of sparkling mineral water.",
            "price": 1.75,
            "size_ml": 500,
            "is_carbonated": True,
            "image_url": "/images/drinks/sparkling_water.jpg",
            "is_available": True,
            "category_name": "Drink"
        },
        {
            "name": "Şalgam",
            "description": "A traditional Turkish drink made from fermented black carrots.",
            "price": 2.25,
            "size_ml": 300,
            "is_carbonated": False,
            "image_url": "/images/drinks/salgam.jpg",
            "is_available": True,
            "category_name": "Drink"
        }
    ]

    for drink_data in drinks_to_seed:
        drink_exists = db.query(Drink).filter(Drink.name == drink_data["name"]).first()
        if not drink_exists:
            category = db.query(Category).filter(Category.name == drink_data["category_name"]).first()
            if not category:
                # If the category doesn't exist, you might want to create it
                # For now, we'll just skip the drink if the category isn't found
                continue

            new_drink = Drink(
                name=drink_data["name"],
                description=drink_data["description"],
                price=drink_data["price"],
                size_ml=drink_data["size_ml"],
                is_carbonated=drink_data["is_carbonated"],
                image_url=drink_data["image_url"],
                is_available=drink_data["is_available"],
                category_id=category.id
            )
            db.add(new_drink)

    db.commit()

    # Print success message
    drinks_count = db.query(Drink).count()
    print(f"Successfully seeded {drinks_count} drinks.")
