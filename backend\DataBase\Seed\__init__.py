# Database seed package initialization
from sqlalchemy.orm import Session
from .seed_admin import seed_admin
from .seed_categories import seed_categories
from .seed_ingredients import seed_ingredients
from .seed_doners import seed_doners
from .seed_kebabs import seed_kebabs
from .seed_salads import seed_salads
from .seed_drinks import seed_drinks
from .seed_menus import seed_menus

def run_all_seeds(db: Session):
    """
    Run all seed functions in the correct order.
    Dependencies are handled by running seeds in the right sequence.
    """
    print("Starting database seeding...")

    try:
        # 1. Seed admin user first
        print("\n1. Seeding admin user...")
        seed_admin(db)

        # 2. Seed categories (required for products)
        print("\n2. Seeding categories...")
        seed_categories(db)

        # 3. Seed ingredients (required for products)
        print("\n3. Seeding ingredients...")
        seed_ingredients(db)

        # 4. Seed products (doners, kebabs, salads, drinks)
        print("\n4. Seeding doners...")
        seed_doners(db)

        print("\n5. Seeding kebabs...")
        seed_kebabs(db)

        print("\n6. Seeding salads...")
        seed_salads(db)

        print("\n7. Seeding drinks...")
        seed_drinks(db)

        # 5. Seed menus (requires products to exist)
        print("\n8. Seeding menus...")
        seed_menus(db)

        print("\n✅ Database seeding completed successfully!")

    except Exception as e:
        print(f"\n❌ Error during seeding: {e}")
        db.rollback()
        raise e
