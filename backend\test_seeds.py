#!/usr/bin/env python3
"""
Test script to run all database seeds and verify the relationships work correctly.
"""

from sqlalchemy.orm import sessionmaker
from DataBase.DataBase import engine, Base
from DataBase.Seed import run_all_seeds
from Models.Category.CategoryModel import Category
from Models.Ingredients.IngredientsModel import Ingredient
from Models.Doners.DonerModel import Doner
from Models.Kebabs.KebabModel import Kebab
from Models.Salads.SaladModel import Salad
from Models.Drinks.DrinksModel import Drink
from Models.Menu.MenuModel import Menu
from Models.User.UserModel import User, UserRole

def test_seeds():
    """Test the seeding process and verify relationships."""
    
    # Create tables
    print("Creating database tables...")
    Base.metadata.create_all(bind=engine)
    
    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Run all seeds
        print("\n" + "="*50)
        print("RUNNING DATABASE SEEDS")
        print("="*50)
        run_all_seeds(db)
        
        # Verify the data
        print("\n" + "="*50)
        print("VERIFYING SEEDED DATA")
        print("="*50)
        
        # Check counts
        categories_count = db.query(Category).count()
        ingredients_count = db.query(Ingredient).count()
        doners_count = db.query(Doner).count()
        kebabs_count = db.query(Kebab).count()
        salads_count = db.query(Salad).count()
        drinks_count = db.query(Drink).count()
        menus_count = db.query(Menu).count()
        users_count = db.query(User).count()
        
        print(f"📊 Data Summary:")
        print(f"   Categories: {categories_count}")
        print(f"   Ingredients: {ingredients_count}")
        print(f"   Doners: {doners_count}")
        print(f"   Kebabs: {kebabs_count}")
        print(f"   Salads: {salads_count}")
        print(f"   Drinks: {drinks_count}")
        print(f"   Menus: {menus_count}")
        print(f"   Users: {users_count}")
        
        # Test relationships
        print(f"\n🔗 Testing Relationships:")
        
        # Test doner with ingredients
        doner = db.query(Doner).first()
        if doner:
            print(f"   Doner '{doner.name}' has {len(doner.ingredients)} ingredients")
            print(f"   Category: {doner.category.name if doner.category else 'None'}")
        
        # Test menu relationships
        menu = db.query(Menu).first()
        if menu:
            print(f"   Menu '{menu.name}' includes:")
            print(f"     - Drink: {menu.drink.name if menu.drink else 'None'}")
            print(f"     - Salad: {menu.salad.name if menu.salad else 'None'}")
            print(f"     - Kebab: {menu.kebab.name if menu.kebab else 'None'}")
            print(f"     - Doner: {menu.doner.name if menu.doner else 'None'}")
            print(f"     - Total Price: ${menu.price}")
        
        # Test admin user
        admin = db.query(User).filter(User.role == UserRole.ADMIN).first()
        if admin:
            print(f"   Admin user: {admin.username} ({admin.email})")
        
        print(f"\n✅ All seeds completed successfully!")
        print(f"✅ All relationships are working correctly!")
        
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        db.rollback()
        raise e
    finally:
        db.close()

if __name__ == "__main__":
    test_seeds()
