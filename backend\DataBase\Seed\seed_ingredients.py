
from sqlalchemy.orm import Session
from ...Models.Ingredients.IngredientsModel import Ingredient

def seed_ingredients(db: Session):
    """
    Seed the database with comprehensive ingredients for all food types.
    """
    # Check if ingredients already exist
    existing_ingredients = db.query(Ingredient).count()
    if existing_ingredients > 0:
        print(f"Ingredients already exist ({existing_ingredients} found), skipping ingredient seeding.")
        return

    # A comprehensive list of ingredients for all food types
    ingredients_to_seed = [
        # Meats
        "Lamb", "Beef", "Chicken", "Veal", "Ground Beef", "Ground Lamb", "Chicken Breast", "Chicken Thigh", "Steak",

        # Seafood
        "Shrimp", "Fish",

        # Vegetables
        "Onion", "Tomato", "Lettuce", "Red Cabbage", "Cucumber", "Olives", "Bell Pepper", "Red Onion", "Parsley",
        "Mint", "Cilantro", "Pickles", "Jalapeños", "Cabbage", "Carrots", "Arugula", "Spinach", "Corn", "Mushrooms",

        # Fruits
        "Lemon", "Pomegranate Seeds",

        # Sauces & Dressings
        "Yogurt Sauce", "Garlic Sauce", "Spicy Sauce", "Vinaigrette Dressing", "Tahini Sauce", "Hummus", "Tzatziki Sauce",
        "BBQ Sauce", "Olive Oil", "Balsamic Vinaigrette", "Ranch Dressing", "Caesar Dressing",

        # Spices & Herbs
        "Black Pepper", "Salt", "Cumin", "Paprika", "Oregano", "Thyme", "Rosemary", "Sumac", "Garlic Powder", "Onion Powder", "Chili Flakes",

        # Breads & Grains
        "Pita Bread", "Flatbread", "Lavash Bread", "Tortilla", "Bulgur", "Quinoa", "Rice",

        # Dairy & Cheese
        "Feta Cheese", "Halloumi Cheese", "Parmesan Cheese", "Mozzarella Cheese", "Sour Cream",

        # Nuts & Seeds
        "Walnuts", "Almonds", "Sunflower Seeds", "Sesame Seeds",

        # Other
        "Pickled Turnips", "Pickled Peppers"
    ]

    # Add ingredients to the database if they don't already exist
    ingredients_added = []
    for ingredient_name in ingredients_to_seed:
        # Check if the ingredient already exists
        db_ingredient = db.query(Ingredient).filter(Ingredient.name == ingredient_name).first()
        if not db_ingredient:
            # If it doesn't exist, create and add it
            new_ingredient = Ingredient(name=ingredient_name)
            db.add(new_ingredient)
            ingredients_added.append(ingredient_name)

    try:
        # Commit all new ingredients to the database
        db.commit()
        print(f"Successfully seeded {len(ingredients_added)} ingredients.")
    except Exception as e:
        db.rollback()
        print(f"Error seeding ingredients: {e}")
        raise e
