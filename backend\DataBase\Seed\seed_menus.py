
from sqlalchemy.orm import Session
from ...Models.Menu.MenuModel import <PERSON>u
from ...Models.Drinks.DrinksModel import Drink
from ...Models.Salads.SaladModel import Salad
from ...Models.Kebabs.KebabModel import Kebab
from ...Models.Doners.DonerModel import Doner

def seed_menus(db: Session):
    # Check if menus already exist
    existing_menus = db.query(Menu).count()
    if existing_menus > 0:
        print(f"Menus already exist ({existing_menus} found), skipping menu seeding.")
        return

    menus_to_seed = [
        {
            "name": "Adana Kebab Menu",
            "description": "A full meal featuring our spicy Adana Kebab, a fresh salad, and a cold drink.",
            "kebab_name": "Adana Kebab",
            "salad_name": "Shepherd's Salad (Çoban Salata)",
            "drink_name": "Ayran",
            "image_url": "/images/menus/adana_menu.jpg"
        },
        {
            "name": "Chicken Shish Kebab Menu",
            "description": "A light and healthy menu with grilled chicken shish kebab, a classic salad, and water.",
            "kebab_name": "Chicken Shish Kebab",
            "salad_name": "Classic Salad",
            "drink_name": "Still Water",
            "image_url": "/images/menus/chicken_shish_menu.jpg"
        },
        {
            "name": "Classic Beef Doner Menu",
            "description": "The perfect combo of our classic beef doner, a refreshing salad, and a Coca-Cola.",
            "doner_name": "Classic Beef Doner",
            "salad_name": "Classic Salad",
            "drink_name": "Coca-Cola",
            "image_url": "/images/menus/beef_doner_menu.jpg"
        },
        {
            "name": "Spicy Lamb Doner Menu",
            "description": "For those who like it hot! Our spicy lamb doner with a cooling salad and a drink.",
            "doner_name": "Spicy Lamb Doner",
            "salad_name": "Arugula Salad",
            "drink_name": "Şalgam",
            "image_url": "/images/menus/spicy_lamb_doner_menu.jpg"
        },
        {
            "name": "Vegetarian Kebab Menu",
            "description": "A delicious and satisfying vegetarian meal with our vegetable kebab, a Greek salad, and a Fanta.",
            "kebab_name": "Vegetable Kebab",
            "salad_name": "Greek Salad",
            "drink_name": "Fanta",
            "image_url": "/images/menus/vegetarian_menu.jpg"
        }
    ]

    for menu_data in menus_to_seed:
        menu_exists = db.query(Menu).filter(Menu.name == menu_data["name"]).first()
        if not menu_exists:
            drink = db.query(Drink).filter(Drink.name == menu_data["drink_name"]).first()
            salad = db.query(Salad).filter(Salad.name == menu_data["salad_name"]).first()

            if not (drink and salad):
                continue

            kebab = None
            doner = None
            main_course_price = 0

            if "kebab_name" in menu_data:
                kebab = db.query(Kebab).filter(Kebab.name == menu_data["kebab_name"]).first()
                if kebab:
                    main_course_price = kebab.price
            elif "doner_name" in menu_data:
                doner = db.query(Doner).filter(Doner.name == menu_data["doner_name"]).first()
                if doner:
                    main_course_price = doner.price

            if not (kebab or doner):
                continue

            # Calculate the total price with a 10% discount
            total_price = (main_course_price + salad.price + drink.price) * 0.9

            new_menu = Menu(
                name=menu_data["name"],
                description=menu_data["description"],
                price=round(total_price, 2),
                image_url=menu_data["image_url"],
                is_available=True,
                drink_id=drink.id,
                salad_id=salad.id,
                kebab_id=kebab.id if kebab else None,
                doner_id=doner.id if doner else None
            )
            db.add(new_menu)

    db.commit()

    # Print success message
    menus_count = db.query(Menu).count()
    print(f"Successfully seeded {menus_count} menus.")
