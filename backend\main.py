from contextlib import asynccontextmanager
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from dotenv import load_dotenv
import os

# --- Database Imports ---
from .DataBase.DataBase import engine, Base, get_db, init_database_pool, close_database_pool

load_dotenv()

# Import all models to ensure they are registered with SQLAlchemy's Base
from .Models import *
from .Routes.User.UserRoutes import User_Router
from .Routes.User.AddressRoutes import Address_Router
from .Routes.User.CommentRoutes import  Comment_Router
from .Routes.User.ReviewRoutes import Review_Router
from .Routes.Category.CategoryRoutes import Category_Router
from .Routes.Doner.DonerRoutes import Doner_Router
from .Routes.Drink.DrinkRoutes import Drink_Router
from .Routes.Ingredient.IngredientRoutes import Ingredient_Router
from .Routes.Kebab.KebabRoutes import <PERSON><PERSON>b_Router
from .Routes.Menu.MenuRoutes import Menu_Router
from .Routes.Message.MessageRoutes import Message_Router
from .Routes.Order.OrderRoutes import Order_Router
from .Routes.Order.OrderItemRoutes import OrderItem_Router
from .Routes.Salad.SaladRoutes import Salad_Router
from .Routes.Cart.CartRoutes import Cart_Router

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await init_database_pool()
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield
    # Shutdown
    await close_database_pool()

# Application metadata for Swagger UI
app = FastAPI(
    title="E-Commerce API",
    description="The main API for the E-Commerce application, handling products, users, and orders.",
    version="1.0.0",
    contact={
        "name": "API Support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "Apache 2.0",
        "url": "https://www.apache.org/licenses/LICENSE-2.0.html",
    },
    lifespan=lifespan # Use the lifespan manager
)

# CORS (Cross-Origin Resource Sharing) Middleware
origins = [
    "http://localhost",
    "http://localhost:3000",  # Common port for React frontend
    "http://localhost:5173",  # Common port for Vite frontend
    # Add your deployed frontend URL here when you go to production
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(User_Router, prefix="/users", tags=["Users"])
app.include_router(Address_Router, prefix="/users", tags=["Users"])
app.include_router(Comment_Router, prefix="/users", tags=["Users"])
app.include_router(Review_Router, prefix="/users", tags=["Users"])
app.include_router(Category_Router, prefix="/categories", tags=["Categories"])
app.include_router(Doner_Router, prefix="/doners", tags=["Doners"])
app.include_router(Drink_Router, prefix="/drinks", tags=["Drinks"])
app.include_router(Ingredient_Router, prefix="/ingredients", tags=["Ingredients"])
app.include_router(Kebab_Router, prefix="/kebabs", tags=["Kebabs"])
app.include_router(Menu_Router, prefix="/menus", tags=["Menus"])
app.include_router(Message_Router, prefix="/messages", tags=["Messages"])
app.include_router(Order_Router, prefix="/orders", tags=["Orders"])
app.include_router(OrderItem_Router, prefix="/order-items", tags=["Order Items"])
app.include_router(Salad_Router, prefix="/salads", tags=["Salads"])
app.include_router(Cart_Router, prefix="/cart", tags=["Cart"])

# A simple root endpoint
@app.get("/", tags=["Root"])
async def read_root():
    """A simple GET endpoint to confirm the API is running."""
    return {"message": "Welcome to the E-Commerce API!"}

@app.get("/db-check", tags=["Health Check"])
async def db_check(db: AsyncSession = Depends(get_db)):
    try:
        # Use a simple SELECT 1 query to test database connectivity
        result = await db.execute(text("SELECT 1"))
        await db.commit()
        return {"status": "ok", "message": "Database connection successful"}
    except Exception as e:
        # If there's an exception, the connection has failed
        return {"status": "error", "message": f"Database connection failed: {str(e)}"}
