
from sqlalchemy.orm import Session
from ...Models.Salads.SaladModel import Salad
from ...Models.Ingredients.IngredientsModel import Ingredient
from ...Models.Category.CategoryModel import Category

def seed_salads(db: Session):
    """
    Seed the database with salad items and their ingredients.
    """
    # Check if salads already exist
    existing_salads = db.query(Salad).count()
    if existing_salads > 0:
        print(f"Salads already exist ({existing_salads} found), skipping salad seeding.")
        return

    salads_to_seed = [
        {
            "name": "Classic Salad",
            "description": "A classic salad with fresh vegetables, feta cheese, and olives.",
            "price": 9.50,
            "dressing": "Vinaigrette Dressing",
            "is_vegetarian": True,
            "image_url": "/images/salads/greek_salad.jpg",
            "is_available": True,
            "category_name": "Salad",
            "ingredients": ["Lettuce", "Tomato", "Cucumber", "Red Onion", "Olives", "Feta Cheese", "Vinaigrette Dressing"]
        },
        {
            "name": "Chicken Caesar Salad",
            "description": "Grilled chicken breast on a bed of romaine lettuce with Caesar dressing and croutons.",
            "price": 12.00,
            "dressing": "Caesar Dressing",
            "is_vegetarian": False,
            "image_url": "/images/salads/chicken_caesar_salad.jpg",
            "is_available": True,
            "category_name": "Salad",
            "ingredients": ["Chicken Breast", "Lettuce", "Parmesan Cheese", "Caesar Dressing"]
        },
        {
            "name": "Shepherd's Salad (Çoban Salata)",
            "description": "A simple and refreshing Turkish salad with finely chopped tomatoes, cucumbers, onions, and parsley.",
            "price": 8.00,
            "dressing": "Olive Oil & Lemon",
            "is_vegetarian": True,
            "image_url": "/images/salads/shepherds_salad.jpg",
            "is_available": True,
            "category_name": "Salad",
            "ingredients": ["Tomato", "Cucumber", "Onion", "Parsley", "Bell Pepper", "Olive Oil", "Lemon"]
        },
        {
            "name": "Arugula Salad",
            "description": "A peppery arugula salad with walnuts, pomegranate seeds, and a balsamic vinaigrette.",
            "price": 10.50,
            "dressing": "Balsamic Vinaigrette",
            "is_vegetarian": True,
            "image_url": "/images/salads/arugula_salad.jpg",
            "is_available": True,
            "category_name": "Salad",
            "ingredients": ["Arugula", "Walnuts", "Pomegranate Seeds", "Parmesan Cheese", "Balsamic Vinaigrette"]
        },
        {
            "name": "Halloumi Salad",
            "description": "Warm grilled halloumi cheese served over a bed of mixed greens with a light lemon dressing.",
            "price": 13.00,
            "dressing": "Olive Oil & Lemon",
            "is_vegetarian": True,
            "image_url": "/images/salads/halloumi_salad.jpg",
            "is_available": True,
            "category_name": "Salad",
            "ingredients": ["Halloumi Cheese", "Lettuce", "Spinach", "Tomato", "Cucumber", "Lemon", "Olive Oil"]
        }
    ]

    for salad_data in salads_to_seed:
        salad_exists = db.query(Salad).filter(Salad.name == salad_data["name"]).first()
        if not salad_exists:
            category = db.query(Category).filter(Category.name == salad_data["category_name"]).first()
            if not category:
                continue

            new_salad = Salad(
                name=salad_data["name"],
                description=salad_data["description"],
                price=salad_data["price"],
                dressing=salad_data["dressing"],
                is_vegetarian=salad_data["is_vegetarian"],
                image_url=salad_data["image_url"],
                is_available=salad_data["is_available"],
                category_id=category.id
            )

            ingredients = db.query(Ingredient).filter(Ingredient.name.in_(salad_data["ingredients"])).all()
            new_salad.ingredients.extend(ingredients)

            db.add(new_salad)

    try:
        db.commit()
        # Print success message
        salads_count = db.query(Salad).count()
        print(f"Successfully seeded {salads_count} salads.")
    except Exception as e:
        db.rollback()
        print(f"Error seeding salads: {e}")
        raise e
