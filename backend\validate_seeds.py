#!/usr/bin/env python3
"""
Validation script to check if all ingredients referenced in food items exist in the ingredients list.
"""

def validate_ingredient_consistency():
    """Check if all ingredients referenced in food seeds exist in the ingredients list."""
    
    # Import the ingredients list
    from DataBase.Seed.seed_ingredients import seed_ingredients
    from DataBase.Seed.seed_doners import seed_doners
    from DataBase.Seed.seed_kebabs import seed_kebabs
    from DataBase.Seed.seed_salads import seed_salads
    
    # Get ingredients list from seed_ingredients
    ingredients_list = [
        # Meats
        "Lamb", "Beef", "Chicken", "Veal", "Ground Beef", "Ground Lamb", "Chicken Breast", "Chicken Thigh", "Steak",
        # Seafood
        "Shrimp", "Fish",
        # Vegetables
        "Onion", "Tomato", "Lettuce", "Red Cabbage", "Cucumber", "Olives", "Bell Pepper", "Red Onion", "Parsley",
        "Mint", "Cilantro", "Pickles", "Jalapeños", "Cabbage", "Carrots", "Arugula", "Spinach", "Corn", "Mushrooms",
        # Fruits
        "Lemon", "Pomegranate Seeds",
        # Sauces & Dressings
        "Yogurt Sauce", "Garlic Sauce", "Spicy Sauce", "Vinaigrette Dressing", "Tahini Sauce", "Hummus", "Tzatziki Sauce",
        "BBQ Sauce", "Olive Oil", "Balsamic Vinaigrette", "Ranch Dressing", "Caesar Dressing",
        # Spices & Herbs
        "Black Pepper", "Salt", "Cumin", "Paprika", "Oregano", "Thyme", "Rosemary", "Sumac", "Garlic Powder", "Onion Powder", "Chili Flakes",
        # Breads & Grains
        "Pita Bread", "Flatbread", "Lavash Bread", "Tortilla", "Bulgur", "Quinoa", "Rice",
        # Dairy & Cheese
        "Feta Cheese", "Halloumi Cheese", "Parmesan Cheese", "Mozzarella Cheese", "Sour Cream",
        # Nuts & Seeds
        "Walnuts", "Almonds", "Sunflower Seeds", "Sesame Seeds",
        # Other
        "Pickled Turnips", "Pickled Peppers"
    ]
    
    # Collect all ingredients used in food items
    used_ingredients = set()
    
    # Doner ingredients
    doner_ingredients = [
        ["Beef", "Flatbread", "Lettuce", "Tomato", "Onion", "Garlic Sauce"],
        ["Chicken", "Rice", "Lettuce", "Cucumber", "Tomato", "Tzatziki Sauce"],
        ["Lamb", "Flatbread", "Onion", "Jalapeños", "Spicy Sauce", "Red Cabbage"],
        ["Veal", "Pita Bread", "Parsley", "Tomato", "Onion", "Lemon"],
        ["Beef", "Chicken", "Flatbread", "Lettuce", "Tomato", "Onion", "Garlic Sauce", "Spicy Sauce"]
    ]
    
    # Kebab ingredients
    kebab_ingredients = [
        ["Ground Lamb", "Chili Flakes", "Bell Pepper", "Parsley", "Pita Bread"],
        ["Chicken Breast", "Olive Oil", "Lemon", "Yogurt Sauce", "Bell Pepper", "Onion"],
        ["Ground Beef", "Paprika", "Onion", "Parsley", "Pita Bread"],
        ["Lamb", "Pita Bread", "Tomato", "Yogurt Sauce", "Olive Oil"],
        ["Bell Pepper", "Onion", "Tomato", "Mushrooms", "Corn", "Olive Oil", "Oregano"]
    ]
    
    # Salad ingredients
    salad_ingredients = [
        ["Lettuce", "Tomato", "Cucumber", "Red Onion", "Olives", "Feta Cheese", "Vinaigrette Dressing"],
        ["Chicken Breast", "Lettuce", "Parmesan Cheese", "Caesar Dressing"],
        ["Tomato", "Cucumber", "Onion", "Parsley", "Bell Pepper", "Olive Oil", "Lemon"],
        ["Arugula", "Walnuts", "Pomegranate Seeds", "Parmesan Cheese", "Balsamic Vinaigrette"],
        ["Halloumi Cheese", "Lettuce", "Spinach", "Tomato", "Cucumber", "Lemon", "Olive Oil"]
    ]
    
    # Collect all used ingredients
    for ingredient_list in doner_ingredients + kebab_ingredients + salad_ingredients:
        used_ingredients.update(ingredient_list)
    
    # Check for missing ingredients
    missing_ingredients = used_ingredients - set(ingredients_list)
    extra_ingredients = set(ingredients_list) - used_ingredients
    
    print("🔍 INGREDIENT VALIDATION REPORT")
    print("=" * 50)
    
    if missing_ingredients:
        print(f"❌ Missing ingredients (used in food but not in ingredients list):")
        for ingredient in sorted(missing_ingredients):
            print(f"   - {ingredient}")
    else:
        print("✅ All used ingredients are available in the ingredients list!")
    
    print(f"\n📊 Statistics:")
    print(f"   Total ingredients available: {len(ingredients_list)}")
    print(f"   Total ingredients used: {len(used_ingredients)}")
    print(f"   Unused ingredients: {len(extra_ingredients)}")
    
    if extra_ingredients:
        print(f"\n💡 Unused ingredients (available but not used in any food):")
        for ingredient in sorted(extra_ingredients):
            print(f"   - {ingredient}")
    
    return len(missing_ingredients) == 0

if __name__ == "__main__":
    is_valid = validate_ingredient_consistency()
    if is_valid:
        print(f"\n✅ Validation passed! All ingredients are consistent.")
    else:
        print(f"\n❌ Validation failed! Please fix missing ingredients.")
